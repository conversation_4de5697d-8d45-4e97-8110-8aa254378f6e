<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated UI</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div class="relative w-[351px] md:w-auto md:max-w-full lg:max-w-screen-lg h-[161px] md:h-auto p-2 md:p-4 m-2 md:m-4 bg-white rounded-xl md:rounded-2xl shadow-md md:shadow-lg overflow-hidden">
        <input class="relative w-[301px] md:w-auto md:max-w-full lg:max-w-screen-lg h-[41px] md:h-auto flex flex-col justify-start space-y-4 p-2 md:p-4 m-2 md:m-4 px-3 py-2 md:px-4 md:py-3 border border-gray-300 rounded-md md:rounded-lg bg-white text-gray-900 placeholder-gray-400 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" style="background-color: rgb(228, 228, 228); color: black" type="text" placeholder="Enter text here..." />
        <button class="relative w-[151px] md:w-auto md:max-w-full lg:max-w-screen-lg h-[51px] md:h-auto flex flex-col justify-start space-y-4 p-2 md:p-4 m-2 md:m-4 px-4 py-2 md:px-6 md:py-3 rounded-md md:rounded-lg bg-blue-500 hover:bg-blue-600 text-white font-medium shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" style="background-color: rgb(97, 97, 97); color: white">
            Button
        </button>
    </div>
</body>
</html>
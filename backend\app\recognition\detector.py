import cv2
import numpy as np
from typing import List, Dict, Optional
from dataclasses import dataclass

@dataclass
class UIElement:
    """Represents a detected UI element with its properties."""
    type: str
    bbox: tuple  # (x, y, width, height)
    text: Optional[str] = None
    style: Dict[str, str] = None
    children: List['UIElement'] = None
    confidence: float = 1.0

    def __post_init__(self):
        if self.style is None:
            self.style = {}
        if self.children is None:
            self.children = []

class UIElementDetector:
    """Detects UI elements in screenshots using computer vision and ML techniques."""
    
    def __init__(self):
        # Initialize detection parameters
        self.min_confidence = 0.7
        self.supported_elements = ['button', 'input', 'text', 'image', 'container']

    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocesses the input image for better element detection."""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY_INV, 11, 2
        )
        return thresh

    def detect_elements(self, image: np.ndarray) -> List[UIElement]:
        """Detects UI elements in the preprocessed image."""
        elements = []
        processed = self.preprocess_image(image)

        # Find contours for element detection
        contours, _ = cv2.findContours(
            processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )

        for cnt in contours:
            # Get bounding box
            x, y, w, h = cv2.boundingRect(cnt)

            # Filter out very small contours but allow text elements
            if w < 15 or h < 8:
                continue

            # Basic element classification based on shape and size
            element_type = self._classify_element(w, h, cnt)
            if element_type:
                # Extract element region
                element_region = image[y:y+h, x:x+w] if y+h <= image.shape[0] and x+w <= image.shape[1] else image[y:min(y+h, image.shape[0]), x:min(x+w, image.shape[1])]

                # Extract text and style
                text_content = self._extract_text(element_region, element_type)
                style = self._extract_style(element_region)

                element = UIElement(
                    type=element_type,
                    bbox=(x, y, w, h),
                    text=text_content,
                    style=style
                )
                elements.append(element)

        return elements

    def _classify_element(self, width: int, height: int, contour: np.ndarray) -> Optional[str]:
        """Classifies UI element based on shape and size characteristics."""
        aspect_ratio = width / float(height) if height > 0 else 0
        area = cv2.contourArea(contour)

        # Filter out very small elements
        if width < 15 or height < 8 or area < 100:
            return None

        # More refined classification based on size and aspect ratio
        if 3 <= aspect_ratio <= 15 and 15 <= height <= 50 and area > 500:
            return 'input'  # Wide, relatively short elements
        elif 1 <= aspect_ratio <= 4 and 20 <= height <= 60 and area > 300:
            return 'button'  # Rectangular button-like elements
        elif aspect_ratio > 5 and height < 25 and area > 100:
            return 'text'  # Wide, short elements (text lines)
        elif 0.8 <= aspect_ratio <= 1.5 and area > 1000:
            return 'image'  # Square-ish larger elements
        elif area > 800:
            return 'container'  # Larger elements that don't fit other categories

        return 'text'  # Default to text for smaller elements that don't fit other categories

    def _extract_text(self, element_image: np.ndarray, element_type: str) -> Optional[str]:
        """Extracts text content from element image using simple heuristics."""
        if element_image.size == 0:
            return None

        # Generate placeholder text based on element type
        text_mapping = {
            'button': 'Button',
            'input': 'Enter text here...',
            'text': 'Sample text content',
            'image': '',
            'container': ''
        }

        # For now, return placeholder text based on element type
        # In a real implementation, you would use OCR (like Tesseract)
        return text_mapping.get(element_type, '')

    def _extract_style(self, element_image: np.ndarray) -> Dict[str, str]:
        """Extracts style properties from the element image."""
        style = {}

        # Extract background color
        if element_image.size > 0:
            mean_color = cv2.mean(element_image)[:3]
            # Convert BGR to RGB
            style['background-color'] = f'rgb({int(mean_color[2])}, {int(mean_color[1])}, {int(mean_color[0])})'

            # Determine if it's a dark or light background for text color
            brightness = (mean_color[0] + mean_color[1] + mean_color[2]) / 3
            if brightness < 128:
                style['color'] = 'white'
            else:
                style['color'] = 'black'

        return style
#!/usr/bin/env python3
"""
SnapCode API Endpoint Testing Script

This script tests all the main API endpoints to validate functionality.
"""

import base64
import json
import requests
import time
from PIL import Image, ImageDraw
import io

def create_test_image():
    """Create a simple test image with UI elements."""
    img = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img)

    # Draw a button-like rectangle with more contrast
    draw.rectangle([30, 30, 150, 70], fill='#3B82F6', outline='#1E40AF', width=2)
    draw.text((60, 45), 'Submit', fill='white')

    # Draw an input-like rectangle with border
    draw.rectangle([30, 100, 350, 140], fill='white', outline='#D1D5DB', width=2)
    draw.text((40, 115), 'Enter your email...', fill='#9CA3AF')

    # Draw a text label above the input
    draw.text((30, 80), 'Email Address:', fill='black')

    # Draw another button
    draw.rectangle([200, 30, 320, 70], fill='#10B981', outline='#059669', width=2)
    draw.text((230, 45), 'Cancel', fill='white')

    # Draw some descriptive text
    draw.text((30, 170), 'Please fill out the form below', fill='#374151')

    # Draw a container/card background
    draw.rectangle([20, 20, 370, 200], fill=None, outline='#E5E7EB', width=1)

    # Convert to base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return base64.b64encode(buffer.getvalue()).decode()

def test_health_endpoint():
    """Test the health check endpoint."""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get('http://localhost:8000/health', timeout=10)
        print(f"✅ Health check: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Status: {data.get('status')}")
            print(f"   Version: {data.get('version')}")
            print(f"   Uptime: {data.get('uptime'):.2f}s")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_frameworks_endpoint():
    """Test the frameworks endpoint."""
    print("🔍 Testing frameworks endpoint...")
    try:
        response = requests.get('http://localhost:8000/api/frameworks', timeout=10)
        print(f"✅ Frameworks: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Supported: {data.get('frameworks')}")
            print(f"   Default: {data.get('default')}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Frameworks test failed: {e}")
        return False

def test_conversion_endpoint(framework='react'):
    """Test the conversion endpoint with a specific framework."""
    print(f"🔍 Testing conversion endpoint with {framework}...")
    
    img_base64 = create_test_image()
    
    payload = {
        'image': img_base64,
        'framework': framework,
        'options': {
            'styling': 'tailwind',
            'typescript': True,
            'responsive': True,
            'accessibility': True
        }
    }
    
    try:
        response = requests.post('http://localhost:8000/api/convert', json=payload, timeout=30)
        print(f"✅ Conversion ({framework}): {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                code_result = result.get('result', {})
                print(f"   Framework: {code_result.get('framework')}")
                print(f"   Code length: {len(code_result.get('code', ''))}")
                print(f"   Components: {len(code_result.get('components', []))}")

                # Save the generated HTML for inspection
                if framework == 'html':
                    with open(f'generated_{framework}.html', 'w', encoding='utf-8') as f:
                        f.write(code_result.get('code', ''))
                    print(f"   💾 Saved generated HTML to generated_{framework}.html")

                    # Print first few lines of generated code for inspection
                    code_lines = code_result.get('code', '').split('\n')
                    print(f"   📄 First 10 lines of generated code:")
                    for i, line in enumerate(code_lines[:10]):
                        print(f"      {i+1:2d}: {line}")
                    if len(code_lines) > 10:
                        print(f"      ... ({len(code_lines) - 10} more lines)")

                return True
            else:
                print(f"   Error: {result.get('error')}")
                return False
        else:
            print(f"   HTTP Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Conversion test failed: {e}")
        return False

def test_status_endpoint():
    """Test the status endpoint."""
    print("🔍 Testing status endpoint...")
    try:
        response = requests.get('http://localhost:8000/api/status', timeout=10)
        print(f"✅ Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Status: {data.get('status')}")
            print(f"   Environment: {data.get('environment')}")
            print(f"   Rate limit: {data.get('rate_limit', {}).get('enabled')}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Status test failed: {e}")
        return False

def main():
    """Run all API tests."""
    print("🧪 SnapCode API Endpoint Testing")
    print("=" * 50)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(3)
    
    results = []
    
    # Test basic endpoints
    results.append(test_health_endpoint())
    results.append(test_frameworks_endpoint())
    results.append(test_status_endpoint())
    
    # Test conversion with different frameworks
    frameworks = ['html', 'react', 'swift']
    for framework in frameworks:
        results.append(test_conversion_endpoint(framework))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! SnapCode API is working correctly.")
        return 0
    else:
        print("💥 Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())

#!/usr/bin/env python3
"""
Test the improved conversion
"""

import sys
sys.path.append('.')
from debug_detection import create_simple_test_image
import requests
import json

def test_improved_conversion():
    image_data = create_simple_test_image()
    payload = {
        'image': image_data,
        'framework': 'html',
        'options': {
            'styling': 'tailwind',
            'typescript': False,
            'responsive': True,
            'accessibility': True
        }
    }

    response = requests.post('http://localhost:8000/api/convert', json=payload, timeout=30)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            code_result = result.get('result', {})
            print(f'Framework: {code_result.get("framework")}')
            print(f'Code length: {len(code_result.get("code", ""))}')
            print(f'Components: {len(code_result.get("components", []))}')
            
            with open('improved_generated.html', 'w', encoding='utf-8') as f:
                f.write(code_result.get('code', ''))
            print('💾 Saved to improved_generated.html')
            
            # Print first few lines
            code_lines = code_result.get('code', '').split('\n')
            print('📄 First 15 lines:')
            for i, line in enumerate(code_lines[:15]):
                print(f'  {i+1:2d}: {line}')
        else:
            print(f'Error: {result.get("error")}')
    else:
        print(f'HTTP Error: {response.text}')

if __name__ == "__main__":
    test_improved_conversion()

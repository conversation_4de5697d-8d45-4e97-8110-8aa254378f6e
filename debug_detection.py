#!/usr/bin/env python3
"""
Debug script to test the detection pipeline
"""

import base64
import json
import requests
from PIL import Image, ImageDraw
import io

def create_simple_test_image():
    """Create a simple test image with clear UI elements."""
    img = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw a clear button with high contrast
    draw.rectangle([50, 50, 200, 100], fill='#3B82F6', outline='#1E40AF', width=3)
    draw.text((100, 70), 'Submit', fill='white')
    
    # Draw a clear input field
    draw.rectangle([50, 120, 350, 160], fill='white', outline='#374151', width=2)
    draw.text((60, 135), 'Enter text here...', fill='#6B7280')
    
    # Draw text label
    draw.text((50, 90), 'Click the button:', fill='black')
    
    # Convert to base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return base64.b64encode(buffer.getvalue()).decode()

def test_detection():
    """Test the detection endpoint."""
    print("🔍 Testing detection with debug endpoint...")
    
    image_data = create_simple_test_image()
    payload = {
        'image': image_data,
        'framework': 'html',
        'options': {
            'styling': 'tailwind',
            'typescript': False,
            'responsive': True,
            'accessibility': True
        }
    }
    
    try:
        response = requests.post(
            'http://localhost:8000/debug/test-detection-detailed',
            json=payload,
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Image shape: {data.get('image_shape')}")
            print(f"Processed shape: {data.get('processed_shape')}")
            print(f"Elements detected: {data.get('elements_detected')}")
            
            for i, elem in enumerate(data.get('elements', [])):
                print(f"\nElement {i+1}:")
                print(f"  Type: {elem['type']}")
                print(f"  BBox: {elem['bbox']}")
                print(f"  Text: '{elem['text']}'")
                print(f"  Style: {elem['style']}")
                print(f"  Confidence: {elem['confidence']}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_detection()

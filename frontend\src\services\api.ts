/**
 * API service layer for SnapCode frontend
 * Handles all HTTP communication with the backend API
 */

import axios, { AxiosInstance, AxiosError, AxiosResponse } from 'axios';
import {
  ApiResponse,
  ConversionRequest,
  ConversionResult,
  HealthCheckResponse,
  FrameworksResponse,
  StatusResponse,
  ApiError,
} from '@/types';

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || (import.meta.env.DEV ? '' : 'http://localhost:8000');
const API_TIMEOUT = 30000; // 30 seconds

/**
 * Custom API error class
 */
export class SnapCodeApiError extends Error implements ApiError {
  public status: number;
  public code?: string;

  constructor(message: string, status: number, code?: string) {
    super(message);
    this.name = 'SnapCodeApiError';
    this.status = status;
    this.code = code;
  }
}

/**
 * API client class with interceptors and error handling
 */
class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add request timestamp for debugging
        config.metadata = { startTime: new Date() };
        
        // Log request in development
        if (import.meta.env.DEV) {
          console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        }
        
        return config;
      },
      (error) => {
        console.error('❌ Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        // Calculate request duration
        const duration = new Date().getTime() - response.config.metadata?.startTime?.getTime();
        
        // Log response in development
        if (import.meta.env.DEV) {
          console.log(`✅ API Response: ${response.status} (${duration}ms)`);
        }
        
        return response;
      },
      (error: AxiosError) => {
        const duration = error.config?.metadata?.startTime 
          ? new Date().getTime() - error.config.metadata.startTime.getTime()
          : 0;

        console.error(`❌ API Error: ${error.response?.status || 'Network'} (${duration}ms)`, error);
        
        return Promise.reject(this.handleApiError(error));
      }
    );
  }

  /**
   * Handle and transform API errors
   */
  private handleApiError(error: AxiosError): SnapCodeApiError {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      const message = (data as any)?.detail || (data as any)?.message || error.message;
      const code = (data as any)?.code;
      
      return new SnapCodeApiError(message, status, code);
    } else if (error.request) {
      // Request was made but no response received
      return new SnapCodeApiError(
        'Network error: Unable to connect to the server',
        0,
        'NETWORK_ERROR'
      );
    } else {
      // Something else happened
      return new SnapCodeApiError(error.message, 0, 'UNKNOWN_ERROR');
    }
  }

  /**
   * Generic GET request
   */
  async get<T>(url: string): Promise<T> {
    const response = await this.client.get<T>(url);
    return response.data;
  }

  /**
   * Generic POST request
   */
  async post<T, D = any>(url: string, data?: D): Promise<T> {
    const response = await this.client.post<T>(url, data);
    return response.data;
  }

  /**
   * Generic PUT request
   */
  async put<T, D = any>(url: string, data?: D): Promise<T> {
    const response = await this.client.put<T>(url, data);
    return response.data;
  }

  /**
   * Generic DELETE request
   */
  async delete<T>(url: string): Promise<T> {
    const response = await this.client.delete<T>(url);
    return response.data;
  }
}

// Create API client instance
const apiClient = new ApiClient();

/**
 * API service functions
 */
export const apiService = {
  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<HealthCheckResponse> {
    return apiClient.get<HealthCheckResponse>('/health');
  },

  /**
   * Get supported frameworks
   */
  async getFrameworks(): Promise<FrameworksResponse> {
    return apiClient.get<FrameworksResponse>('/api/frameworks');
  },

  /**
   * Get API status and configuration
   */
  async getStatus(): Promise<StatusResponse> {
    return apiClient.get<StatusResponse>('/api/status');
  },

  /**
   * Convert screenshot to code
   */
  async convertScreenshot(request: ConversionRequest): Promise<ApiResponse<ConversionResult>> {
    return apiClient.post<ApiResponse<ConversionResult>>('/api/convert', request);
  },

  /**
   * Test detection endpoint (development only)
   */
  async testDetection(): Promise<any> {
    return apiClient.get('/debug/test-detection');
  },

  /**
   * Test generation endpoint (development only)
   */
  async testGeneration(): Promise<any> {
    return apiClient.get('/debug/test-generation');
  },
};

/**
 * Utility functions for API operations
 */
export const apiUtils = {
  /**
   * Check if error is a SnapCode API error
   */
  isApiError(error: unknown): error is SnapCodeApiError {
    return error instanceof SnapCodeApiError;
  },

  /**
   * Get user-friendly error message
   */
  getErrorMessage(error: unknown): string {
    if (this.isApiError(error)) {
      switch (error.code) {
        case 'NETWORK_ERROR':
          return 'Unable to connect to the server. Please check your internet connection.';
        case 'RATE_LIMIT_EXCEEDED':
          return 'Too many requests. Please wait a moment before trying again.';
        case 'INVALID_IMAGE':
          return 'Invalid image format. Please upload a valid image file.';
        case 'IMAGE_TOO_LARGE':
          return 'Image file is too large. Please upload a smaller image.';
        default:
          return error.message;
      }
    }
    
    if (error instanceof Error) {
      return error.message;
    }
    
    return 'An unexpected error occurred. Please try again.';
  },

  /**
   * Check if error is retryable
   */
  isRetryableError(error: unknown): boolean {
    if (this.isApiError(error)) {
      return error.status >= 500 || error.code === 'NETWORK_ERROR';
    }
    return false;
  },

  /**
   * Convert file to base64
   */
  async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data URL prefix (data:image/jpeg;base64,)
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(file);
    });
  },

  /**
   * Validate image file
   */
  validateImageFile(file: File): { valid: boolean; error?: string } {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Please upload a JPEG, PNG, or WebP image.',
      };
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File is too large. Please upload an image smaller than 10MB.',
      };
    }

    return { valid: true };
  },
};

export default apiService;

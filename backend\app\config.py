"""
SnapCode Configuration Module

This module handles all application configuration including environment variables,
settings validation, and configuration for different deployment environments.
"""

import os
from typing import Optional, List
try:
    from pydantic_settings import BaseSettings
    from pydantic import validator
except ImportError:
    from pydantic import BaseSettings, validator
from enum import Enum

class Environment(str, Enum):
    """Application environment types."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"

class LogLevel(str, Enum):
    """Logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application settings
    app_name: str = "SnapCode"
    app_version: str = "1.0.0"
    app_description: str = "AI-Powered UI Screenshot to Code Converter"
    
    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    environment: Environment = Environment.DEVELOPMENT
    log_level: LogLevel = LogLevel.INFO
    
    # API settings
    api_prefix: str = "/api"
    docs_url: str = "/docs"
    redoc_url: str = "/redoc"
    openapi_url: str = "/openapi.json"
    
    # Rate limiting settings
    rate_limit_requests: int = 100
    rate_limit_window: int = 3600  # 1 hour in seconds
    rate_limit_enabled: bool = True
    
    # Redis settings for rate limiting and caching
    redis_url: str = "redis://localhost:6379"
    redis_enabled: bool = False
    
    # File upload settings
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_image_types: List[str] = ["image/jpeg", "image/png", "image/webp"]
    
    # Code generation settings
    default_framework: str = "react"
    supported_frameworks: List[str] = ["html", "react", "vue", "swift"]
    max_generation_time: int = 30  # seconds
    
    # Security settings
    cors_origins: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:3001", "http://127.0.0.1:3001"]
    cors_allow_credentials: bool = True
    cors_allow_methods: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    cors_allow_headers: List[str] = ["*"]
    
    # Monitoring and health check settings
    health_check_enabled: bool = True
    metrics_enabled: bool = True
    
    @validator("environment", pre=True)
    def validate_environment(cls, v):
        """Validate environment setting."""
        if isinstance(v, str):
            return Environment(v.lower())
        return v
    
    @validator("log_level", pre=True)
    def validate_log_level(cls, v):
        """Validate log level setting."""
        if isinstance(v, str):
            return LogLevel(v.upper())
        return v
    
    @validator("cors_origins", pre=True)
    def validate_cors_origins(cls, v):
        """Parse CORS origins from string if needed."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("supported_frameworks", pre=True)
    def validate_supported_frameworks(cls, v):
        """Parse supported frameworks from string if needed."""
        if isinstance(v, str):
            return [framework.strip() for framework in v.split(",")]
        return v
    
    @validator("allowed_image_types", pre=True)
    def validate_allowed_image_types(cls, v):
        """Parse allowed image types from string if needed."""
        if isinstance(v, str):
            return [mime_type.strip() for mime_type in v.split(",")]
        return v
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        # Environment variable prefixes
        env_prefix = ""
        
        # Field aliases for environment variables
        fields = {
            "redis_url": {"env": "REDIS_URL"},
            "cors_origins": {"env": "CORS_ORIGINS"},
            "supported_frameworks": {"env": "SUPPORTED_FRAMEWORKS"},
            "allowed_image_types": {"env": "ALLOWED_IMAGE_TYPES"},
        }

# Create global settings instance
settings = Settings()

# Development-specific overrides
if settings.environment == Environment.DEVELOPMENT:
    settings.debug = True
    settings.log_level = LogLevel.DEBUG
    settings.redis_enabled = False  # Disable Redis in development by default

# Production-specific overrides
elif settings.environment == Environment.PRODUCTION:
    settings.debug = False
    settings.log_level = LogLevel.INFO
    settings.redis_enabled = True
    settings.rate_limit_enabled = True

# Testing-specific overrides
elif settings.environment == Environment.TESTING:
    settings.debug = True
    settings.log_level = LogLevel.DEBUG
    settings.redis_enabled = False
    settings.rate_limit_enabled = False

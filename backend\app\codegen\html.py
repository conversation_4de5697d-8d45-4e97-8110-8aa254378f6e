import logging
from typing import List, Dict, Optional, Any
from functools import lru_cache
from ..recognition.detector import UIElement

class HTMLGenerationError(Exception):
    """Custom exception for HTML generation errors."""
    pass

class InvalidElementError(HTMLGenerationError):
    """Exception raised for invalid UI elements."""
    pass

class HTMLCodeGenerator:
    """Generates HTML and CSS code from UI elements with modern styling."""

    SUPPORTED_FRAMEWORKS = ['tailwind', 'bootstrap', 'vanilla']

    def __init__(self, framework: str = 'tailwind', cache_size: int = 128, responsive: bool = True):
        """Initialize the HTML code generator.

        Args:
            framework: CSS framework to use ('tailwind', 'bootstrap', 'vanilla')
            cache_size: Size of the LRU cache for class mappings
            responsive: Whether to generate responsive CSS classes

        Raises:
            ValueError: If framework is not supported
        """
        if framework not in self.SUPPORTED_FRAMEWORKS:
            raise ValueError(f"Unsupported framework: {framework}. Must be one of {self.SUPPORTED_FRAMEWORKS}")

        self.framework = framework
        self.responsive = responsive
        self.indent = '    '
        self.logger = logging.getLogger(__name__)
        self.class_mappings = self._init_class_mappings()
        self._setup_logging()

    def _setup_logging(self) -> None:
        """Configure logging for the HTML generator."""
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

    def _validate_element(self, element: Optional[UIElement]) -> None:
        """Validate a UI element before processing.

        Args:
            element: The UI element to validate

        Raises:
            InvalidElementError: If element is invalid
        """
        if not element:
            raise InvalidElementError("Element cannot be None")
        if not hasattr(element, 'type') or not element.type:
            raise InvalidElementError("Element must have a valid type")
        if not hasattr(element, 'bbox') or not all(isinstance(x, (int, float)) for x in element.bbox):
            raise InvalidElementError("Element must have valid bounding box coordinates")

    def generate(self, root: UIElement) -> Dict[str, str]:
        """Generates HTML and CSS code from the element hierarchy.

        Args:
            root: The root UI element

        Returns:
            Dict containing generated HTML and CSS

        Raises:
            InvalidElementError: If root element is invalid
            HTMLGenerationError: If generation fails
        """
        try:
            self._validate_element(root)
            self.logger.info(f"Starting HTML generation with framework: {self.framework}")
            html = []
            css = []

            # Add HTML5 doctype and basic structure
            html.extend([
                '<!DOCTYPE html>',
                '<html lang="en">',
                '<head>',
                '    <meta charset="UTF-8">',
                '    <meta name="viewport" content="width=device-width, initial-scale=1.0">',
                '    <title>Generated UI</title>'
            ])

            # Add framework-specific styles
            if self.framework == 'tailwind':
                html.append('    <script src="https://cdn.tailwindcss.com"></script>')

            html.append('</head>')
            html.append('<body>')

            # Generate main content
            html.extend(self._generate_element(root, 1))

            html.append('</body>')
            html.append('</html>')

            return {
                'html': '\n'.join(html),
                'css': '\n'.join(css) if css else '',
                'framework': self.framework
            }

        except Exception as e:
            self.logger.error(f"HTML generation failed: {str(e)}")
            raise HTMLGenerationError(f"Generation failed: {str(e)}")

    def _generate_element(self, element: UIElement, depth: int) -> List[str]:
        """Recursively generates HTML for an element and its children."""
        if not element:
            return []
            
        lines = []
        indent = self.indent * depth

        try:
            # Generate element's HTML tag and classes
            tag = self._get_html_tag(element)
            classes = self._get_element_classes(
                element.type,
                element.bbox[2],
                element.bbox[3],
                element.style.get('align', '')
            )
            style = self._get_inline_styles(element)

            # Build opening tag with attributes
            attributes = [f'class="{classes}"'] if classes else []
            if style:
                attributes.append(f'style="{style}"')

            # Add element-specific attributes
            if element.type == 'input':
                attributes.append('type="text"')
                if element.text:
                    attributes.append(f'placeholder="{element.text}"')
            elif element.type == 'button' and element.text:
                # Button text will be added as content, not attribute
                pass
            elif element.type == 'image':
                attributes.append('src="#"')
                attributes.append('alt="Generated image"')

            opening_tag = f'{indent}<{tag}'
            if attributes:
                opening_tag += ' ' + ' '.join(attributes)

            # Self-closing tags
            if tag in ['input', 'img', 'br', 'hr']:
                opening_tag += ' />'
                lines.append(opening_tag)
                return lines
            else:
                opening_tag += '>'

            self.logger.debug(f"Generated opening tag at depth {depth}: {opening_tag}")

            lines.append(opening_tag)

            # Add element content (but not for input elements as they use placeholder)
            if element.text and element.text.strip() and element.type not in ['input', 'image']:
                # Escape HTML special characters
                escaped_text = element.text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                lines.append(f'{indent}{self.indent}{escaped_text}')

            # Recursively generate children
            for child in element.children:
                lines.extend(self._generate_element(child, depth + 1))

            # Add closing tag
            lines.append(f'{indent}</{tag}>')

            return lines

        except Exception as e:
            self.logger.error(f"Error generating element at depth {depth}: {str(e)}")
            raise HTMLGenerationError(f"Failed to generate element: {str(e)}")

    def _get_html_tag(self, element: UIElement) -> str:
        """Determines appropriate HTML tag for the element type."""
        tag_mapping = {
            'button': 'button',
            'input': 'input',
            'text': 'p',
            'image': 'img',
            'container': 'div',
            'link': 'a',
            'heading': 'h1',
            'subheading': 'h2',
            'list': 'ul',
            'list-item': 'li',
            'table': 'table',
            'table-row': 'tr',
            'table-cell': 'td',
            'form': 'form',
            'label': 'label',
            'select': 'select',
            'option': 'option',
            'textarea': 'textarea',
            'checkbox': 'input',
            'radio': 'input'
        }

        # Special handling for input elements
        if element.type == 'input':
            # Add type attribute for input elements
            return 'input'

        return tag_mapping.get(element.type, 'div')

    @lru_cache(maxsize=128)
    def _get_element_classes(self, element_type: str, width: int, height: int, align: str) -> str:
        """Generates and caches framework-specific classes based on element properties.

        Args:
            element_type: Type of the UI element
            width: Element width
            height: Element height
            align: Element alignment

        Returns:
            String of space-separated CSS classes
        """
        try:
            classes = []

            if self.framework == 'tailwind':
                # Add positioning and layout classes
                classes.append('relative')

                # Add responsive sizing classes
                w, h = width, height
                if w > 0:
                    classes.extend([
                        f'w-[{w}px]',
                        'md:w-auto md:max-w-full',
                        'lg:max-w-screen-lg'
                    ])
                if h > 0:
                    classes.extend([
                        f'h-[{h}px]',
                        'md:h-auto'
                    ])

                # Add alignment and spacing classes
                if align == 'horizontal':
                    classes.extend([
                        'flex items-center justify-start',
                        'space-x-4'
                    ])
                elif align == 'vertical':
                    classes.extend([
                        'flex flex-col justify-start',
                        'space-y-4'
                    ])

                # Add responsive padding and margin
                classes.extend([
                    'p-2 md:p-4',
                    'm-2 md:m-4'
                ])

                # Add element-specific classes
                type_classes = self.class_mappings.get(element_type, [])
                classes.extend(type_classes)

            return ' '.join(classes)

        except Exception as e:
            self.logger.error(f"Error generating classes: {str(e)}")
            return ""

    def _get_inline_styles(self, element: UIElement) -> str:
        """Generates inline styles for properties not handled by classes.
        
        Args:
            element: The UI element to generate styles for
            
        Returns:
            String of CSS inline styles
            
        Raises:
            HTMLGenerationError: If style generation fails
        """
        try:
            styles = []
        
            # Validate style properties
            style_dict = element.style if isinstance(element.style, dict) else {}
            
            # Handle colors that can't be handled by Tailwind classes
            color_props = ['background-color', 'color', 'border-color']
            for prop in color_props:
                if prop in style_dict:
                    if self._is_valid_color(style_dict[prop]):
                        styles.append(f'{prop}: {style_dict[prop]}')
                    else:
                        self.logger.warning(f"Invalid color value for {prop}: {style_dict[prop]}")
            
            # Handle custom dimensions and positioning
            dimension_props = ['min-width', 'max-width', 'min-height', 'max-height']
            for prop in dimension_props:
                if prop in style_dict:
                    try:
                        value = float(style_dict[prop])
                        if value >= 0:
                            styles.append(f'{prop}: {value}px')
                        else:
                            self.logger.warning(f"Negative value for {prop}: {value}")
                    except (ValueError, TypeError):
                        self.logger.warning(f"Invalid dimension value for {prop}: {style_dict[prop]}")
            
            # Handle custom transforms and transitions
            transform_props = ['transform', 'transition']
            for prop in transform_props:
                if prop in style_dict:
                    styles.append(f'{prop}: {style_dict[prop]}')
            
            self.logger.debug(f"Generated inline styles: {'; '.join(styles)}")
            return '; '.join(styles)
        except Exception as e:
            self.logger.error(f"Error generating inline styles: {str(e)}")
            raise HTMLGenerationError(f"Failed to generate inline styles: {str(e)}")

    def _is_valid_color(self, color: str) -> bool:
        """Validate color values.
        
        Args:
            color: Color value to validate
            
        Returns:
            bool: True if color is valid
        """
        if not color:
            return False
            
        # Check hex colors
        if color.startswith('#'):
            return len(color) in [4, 7, 9] and all(c in '0123456789ABCDEFabcdef' for c in color[1:])
            
        # Check rgb/rgba colors
        if color.startswith(('rgb(', 'rgba(')) and color.endswith(')'):
            return True
            
        # Check named colors (basic validation)
        return color.lower() in ['white', 'black', 'red', 'blue', 'green', 'yellow', 'transparent']

    @lru_cache(maxsize=1)
    def _init_class_mappings(self) -> Dict[str, List[str]]:
        """Initializes and caches framework-specific class mappings for element types.
        
        Returns:
            Dict mapping element types to lists of framework classes
            
        Raises:
            HTMLGenerationError: If mapping initialization fails
        """
        try:
            if self.framework == 'tailwind':
                self.logger.info("Initializing Tailwind class mappings")
                return {
                'button': [
                    'px-4 py-2 md:px-6 md:py-3',
                    'rounded-md md:rounded-lg',
                    'bg-blue-500 hover:bg-blue-600',
                    'text-white font-medium',
                    'shadow-sm hover:shadow-md',
                    'transition-all duration-200',
                    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                ],
                'input': [
                    'px-3 py-2 md:px-4 md:py-3',
                    'border border-gray-300',
                    'rounded-md md:rounded-lg',
                    'bg-white',
                    'text-gray-900 placeholder-gray-400',
                    'shadow-sm',
                    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                ],
                'text': [
                    'text-gray-800',
                    'text-sm md:text-base',
                    'leading-relaxed'
                ],
                'image': [
                    'object-cover object-center',
                    'rounded-lg md:rounded-xl',
                    'shadow-md hover:shadow-lg',
                    'transition-shadow duration-200'
                ],
                'container': [
                    'bg-white',
                    'rounded-xl md:rounded-2xl',
                    'shadow-md md:shadow-lg',
                    'overflow-hidden'
                ]
            }

        except Exception as e:
            self.logger.error(f"Error initializing class mappings: {str(e)}")
            return {}